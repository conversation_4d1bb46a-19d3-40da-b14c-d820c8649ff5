// ------------------
// Create a campaign
// ------------------
// Include the Brevo library
var SibApiV3Sdk = require('sib-api-v3-sdk');
var defaultClient = SibApiV3Sdk.ApiClient.instance;
// Instantiate the client
var apiKey = defaultClient.authentications['api-key'];
apiKey.apiKey = '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';
var apiInstance = new SibApiV3Sdk.EmailCampaignsApi();
var emailCampaigns = new SibApiV3Sdk.CreateEmailCampaign();
// Define the campaign settings
emailCampaigns.name = "Campaign sent via the API";
emailCampaigns.subject = "My subject";
emailCampaigns.sender = {"name": "From name", "email": "<EMAIL>"};
emailCampaigns.type = "classic";
// Content that will be sent
emailCampaigns.htmlContent = 'Congratulations! You successfully sent this example campaign via the Brevo API.';
// Select the recipients
emailCampaigns.recipients = {listIds: [2, 7]};
// Schedule the sending in one hour
emailCampaigns.scheduledAt = '2018-01-01 00:00:01';