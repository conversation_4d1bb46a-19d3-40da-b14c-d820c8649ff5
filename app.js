// ------------------
// Send transactional email
// ------------------
// Include the Brevo library
var SibApiV3Sdk = require('sib-api-v3-sdk');
var defaultClient = SibApiV3Sdk.ApiClient.instance;
// Instantiate the client
var apiKey = defaultClient.authentications['api-key'];
apiKey.apiKey = '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';
var apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
var sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

// Define the email settings
sendSmtpEmail.subject = "Test Email from Brevo API";
sendSmtpEmail.htmlContent = "<html><body><h1>Hello!</h1><p>Congratulations! You successfully sent this email via the Brevo API.</p></body></html>";
sendSmtpEmail.sender = {"name": "From name", "email": "<EMAIL>"};
sendSmtpEmail.to = [
  {"email": "<EMAIL>", "name": "Test Recipient"}
];

console.log('Sending email to:', sendSmtpEmail.to[0].email);

// Send the email
apiInstance.sendTransacEmail(sendSmtpEmail).then(function(data) {
  console.log('API called successfully. Email sent!');
  console.log('Message ID:', data.messageId);
  console.log('Email details:', JSON.stringify(data, null, 2));
}, function(error) {
  console.error('Error sending email:', error);
  if (error.response) {
    console.error('Response status:', error.response.status);
    console.error('Response data:', error.response.data);
  }
});