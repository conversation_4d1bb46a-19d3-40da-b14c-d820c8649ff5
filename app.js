// ------------------
// Send transactional email + Check sender verification
// ------------------
// Include the Brevo library
var SibApiV3Sdk = require('sib-api-v3-sdk');
var defaultClient = SibApiV3Sdk.ApiClient.instance;
// Instantiate the client
var apiKey = defaultClient.authentications['api-key'];
apiKey.apiKey = '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';

// First, let's check sender verification
var sendersApi = new SibApiV3Sdk.SendersApi();
console.log('Checking sender verification...');

sendersApi.getSenders().then(function(data) {
  console.log('Available senders:', JSON.stringify(data.senders, null, 2));

  // Now send the email
  var apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
  var sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

  // Define the email settings
  sendSmtpEmail.subject = "Test Email from Brevo API - " + new Date().toLocaleString();
  sendSmtpEmail.htmlContent = "<html><body><h1>Hello!</h1><p>Congratulations! You successfully sent this email via the Brevo API.</p><p>Sent at: " + new Date().toLocaleString() + "</p></body></html>";
  sendSmtpEmail.textContent = "Hello! Congratulations! You successfully sent this email via the Brevo API. Sent at: " + new Date().toLocaleString();
  sendSmtpEmail.sender = {"name": "seraprogrammer", "email": "<EMAIL>"};
  sendSmtpEmail.to = [
    {"email": "<EMAIL>", "name": "Test Recipient"}
  ];

  console.log('Sending email to:', sendSmtpEmail.to[0].email);
  console.log('From:', sendSmtpEmail.sender.email);

  // Send the email
  return apiInstance.sendTransacEmail(sendSmtpEmail);
}).then(function(data) {
  console.log('✅ API called successfully. Email sent!');
  console.log('📧 Message ID:', data.messageId);
  console.log('📝 Email details:', JSON.stringify(data, null, 2));
  console.log('⏰ Check your email inbox and spam folder in the next few minutes');
}).catch(function(error) {
  console.error('❌ Error:', error);
  if (error.response && error.response.body) {
    console.error('📋 Response details:', JSON.stringify(error.response.body, null, 2));
  }
});